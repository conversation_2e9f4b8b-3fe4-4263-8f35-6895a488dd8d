.statsSection {
  padding-top: var(--section-padding);
  padding-bottom: var(--section-padding);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--gap-padding);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: calc(var(--gap-padding) * 2);
    }
}

.textLabel {
  width: 100%;
  margin-bottom: 1rem;
}

.statCard {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  width: 100%;
}

.statCardBg {
  aspect-ratio: 1 / 1;
  height: auto;
}

.maskWrapper {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.cardContent {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.number {
  font-size: clamp(6rem, 11vw, 11rem);
  color: white;
  line-height: 1;
  font-family: inherit;
}

.label {
  margin-bottom: var(--gap-padding);
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
  width: 100%;
  line-height: 1.4;
  text-align: left;
}

.separator {
  width: 100%;
  height: 1px;
  background-color: #333;
  margin: calc(var(--gap-padding) / 2) 0 0 0;
}
