"use client";

import Description from '@/components/Description';
import Separator from '@/components/Separator';
import { useTranslation } from "@/hooks/useTranslation";

export default function Values() {
  const { t } = useTranslation('pages');

  return (
    <>
      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.1}
        />
      </div>

      <Description
        descriptionTitle={t('agency.values1.title')}
        descriptionText="On accompagne celles et ceux qui voient loin. Pas juste ce qui est à la mode, mais ce que leur marque pourrait représenter dans la durée."
        showButton={false}
        titleTag="h3"
      />

      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.2}
        />
      </div>

      <Description
        descriptionTitle={t('agency.values2.title')}
        descriptionText={t('agency.values2.text')}
        showButton={false}
        titleTag="h3"
      />

      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.3}
        />
      </div>

      <Description
        descriptionTitle={t('agency.values3.title')}
        descriptionText="On creuse. On teste. On apprend. On aime ce qui est différent. Pour nous, rien n’est figé, tout évolue. Chaque projet créatif est l’occasion de pousser les limites encore plus loin."
        showButton={false}
        titleTag="h3"
      />
    </>
  );
}
